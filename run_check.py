#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行一致性检查的示例脚本
"""

import json
from consistency_checker import Consistency<PERSON>hecker
from config import API_CONFIG, DATA_CONFIG

def main():
    print("开始运行一致性检查...")
    
    # 创建检查器实例
    checker = ConsistencyChecker(
        api_base_url=API_CONFIG["base_url"],
        batch_size=API_CONFIG["batch_size"]
    )
    
    try:
        # 运行检查
        result = checker.run_consistency_check(
            excel_path=DATA_CONFIG["excel_file"],
            output_path=DATA_CONFIG["output_file"]
        )
        
        print("\n检查完成！")
        print(f"详细结果已保存到: {DATA_CONFIG['output_file']}")
        
        return result
        
    except Exception as e:
        print(f"检查过程中出现错误: {e}")
        return None

if __name__ == "__main__":
    main()
