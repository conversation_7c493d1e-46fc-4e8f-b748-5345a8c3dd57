#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

# API 配置
API_CONFIG = {
    "base_url": "http://localhost:8080",  # 请根据实际情况修改
    "endpoint": "/requestLargeModel/batchAskToFriday",
    "timeout": 60,
    "batch_size": 5
}

# 数据文件配置
DATA_CONFIG = {
    "excel_file": "新标签28_cut_4_16164674_1752728313.xlsx",
    "output_file": "consistency_check_result.json"
}

# 字段映射配置
FIELD_MAPPING = {
    "context": "context",
    "message": "message", 
    "model": "model",
    "user": "user",
    "role": "role",
    "max_tokens": "max_tokens",
    "temperature": "temperature",
    "top_p": "top_p",
    "top_k": "top_k"
}

# 默认参数
DEFAULT_PARAMS = {
    "invocationMethod": "chat",
    "model": "beauty_model_v2_0827",
    "maxTokens": 500,
    "topP": 0.7,
    "topK": 1,
    "temperature": 0.01,
    "role": "user",
    "timeout": 15000
}
