#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同参数名称和格式的脚本
"""

import requests
import json

def test_parameter_names():
    """测试不同的参数名称"""
    url = "http://10.109.36.153:8080/requestLargeModel/batchAskToFriday"
    headers = {"Content-Type": "application/json"}
    
    # 测试不同的参数名称组合
    test_cases = [
        {
            "name": "原始格式 (bachsize)",
            "payload": {
                "bachsize": 1,
                "fridayHttpRequests": [{
                    "invocationMethod": "text",
                    "model": "beauty_model_v2_0827",
                    "context": "测试",
                    "user": "test"
                }]
            }
        },
        {
            "name": "修正拼写 (batchsize)",
            "payload": {
                "batchsize": 1,
                "fridayHttpRequests": [{
                    "invocationMethod": "text",
                    "model": "beauty_model_v2_0827",
                    "context": "测试",
                    "user": "test"
                }]
            }
        },
        {
            "name": "驼峰命名 (batchSize)",
            "payload": {
                "batchSize": 1,
                "fridayHttpRequests": [{
                    "invocationMethod": "text",
                    "model": "beauty_model_v2_0827",
                    "context": "测试",
                    "user": "test"
                }]
            }
        },
        {
            "name": "下划线命名 (batch_size)",
            "payload": {
                "batch_size": 1,
                "fridayHttpRequests": [{
                    "invocationMethod": "text",
                    "model": "beauty_model_v2_0827",
                    "context": "测试",
                    "user": "test"
                }]
            }
        },
        {
            "name": "不同请求列表名称 (requests)",
            "payload": {
                "bachsize": 1,
                "requests": [{
                    "invocationMethod": "text",
                    "model": "beauty_model_v2_0827",
                    "context": "测试",
                    "user": "test"
                }]
            }
        },
        {
            "name": "简化请求列表名称 (data)",
            "payload": {
                "bachsize": 1,
                "data": [{
                    "invocationMethod": "text",
                    "model": "beauty_model_v2_0827",
                    "context": "测试",
                    "user": "test"
                }]
            }
        }
    ]
    
    for test in test_cases:
        print(f"\n--- 测试: {test['name']} ---")
        print(f"请求体: {json.dumps(test['payload'], ensure_ascii=False, indent=2)}")
        
        try:
            response = requests.post(url, json=test['payload'], headers=headers, timeout=30)
            print(f"状态码: {response.status_code}")
            
            if response.status_code != 400:
                print(f"✅ 可能成功! 响应: {response.text}")
            else:
                print(f"❌ 仍然 400: {response.text[:100]}...")
                
        except Exception as e:
            print(f"请求异常: {e}")

def test_query_vs_body():
    """测试查询参数 vs 请求体参数"""
    base_url = "http://10.109.36.153:8080/requestLargeModel/batchAskToFriday"
    headers = {"Content-Type": "application/json"}
    
    request_data = [{
        "invocationMethod": "text",
        "model": "beauty_model_v2_0827",
        "context": "测试",
        "user": "test"
    }]
    
    test_cases = [
        {
            "name": "bachsize 在请求体中",
            "url": base_url,
            "params": None,
            "body": {
                "bachsize": 1,
                "fridayHttpRequests": request_data
            }
        },
        {
            "name": "bachsize 在查询参数中",
            "url": base_url,
            "params": {"bachsize": 1},
            "body": {"fridayHttpRequests": request_data}
        },
        {
            "name": "batchsize 在查询参数中",
            "url": base_url,
            "params": {"batchsize": 1},
            "body": {"fridayHttpRequests": request_data}
        },
        {
            "name": "两个地方都有",
            "url": base_url,
            "params": {"bachsize": 1},
            "body": {
                "bachsize": 1,
                "fridayHttpRequests": request_data
            }
        }
    ]
    
    for test in test_cases:
        print(f"\n--- 测试: {test['name']} ---")
        print(f"URL: {test['url']}")
        print(f"查询参数: {test['params']}")
        print(f"请求体: {json.dumps(test['body'], ensure_ascii=False, indent=2)}")
        
        try:
            response = requests.post(
                test['url'],
                params=test['params'],
                json=test['body'],
                headers=headers,
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code != 400:
                print(f"✅ 可能成功! 响应: {response.text}")
            else:
                print(f"❌ 仍然 400: {response.text[:100]}...")
                
        except Exception as e:
            print(f"请求异常: {e}")

def test_field_variations():
    """测试字段名称的变体"""
    url = "http://10.109.36.153:8080/requestLargeModel/batchAskToFriday"
    headers = {"Content-Type": "application/json"}
    
    # 测试请求对象内部字段的变体
    field_variations = [
        {
            "name": "原始字段名",
            "request": {
                "invocationMethod": "text",
                "model": "beauty_model_v2_0827",
                "context": "测试",
                "user": "test"
            }
        },
        {
            "name": "下划线字段名",
            "request": {
                "invocation_method": "text",
                "model": "beauty_model_v2_0827",
                "context": "测试",
                "user": "test"
            }
        },
        {
            "name": "简化字段名",
            "request": {
                "method": "text",
                "model": "beauty_model_v2_0827",
                "context": "测试",
                "user": "test"
            }
        },
        {
            "name": "type 字段",
            "request": {
                "type": "text",
                "model": "beauty_model_v2_0827",
                "context": "测试",
                "user": "test"
            }
        }
    ]
    
    for variation in field_variations:
        payload = {
            "bachsize": 1,
            "fridayHttpRequests": [variation['request']]
        }
        
        print(f"\n--- 测试: {variation['name']} ---")
        print(f"请求体: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        try:
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            print(f"状态码: {response.status_code}")
            
            if response.status_code != 400:
                print(f"✅ 可能成功! 响应: {response.text}")
            else:
                print(f"❌ 仍然 400: {response.text[:100]}...")
                
        except Exception as e:
            print(f"请求异常: {e}")

def main():
    """主函数"""
    print("开始参数名称调试...")
    
    # 1. 测试参数名称
    test_parameter_names()
    
    # 2. 测试查询参数 vs 请求体
    test_query_vs_body()
    
    # 3. 测试字段名称变体
    test_field_variations()

if __name__ == "__main__":
    main()
