# API 一致性检查工具

这个工具用于根据 markdown 接口文档调用 batchCallLargeModel API，并将返回结果与 Excel 文件中的 message 字段进行一致性对比。

## 功能特性

- 📊 从 Excel 文件读取测试数据
- 🔄 根据接口文档构造批量 API 请求
- 🚀 调用 batchCallLargeModel API
- 📈 计算一致性比率
- 📝 生成详细的对比报告

## 文件结构

```
.
├── batchCallLargeModel_API_Documentation.md  # API 接口文档
├── 新标签28_cut_4_16164674_1752728313.xlsx   # 测试数据文件
├── consistency_checker.py                    # 主要检查工具
├── config.py                                # 配置文件
├── run_check.py                             # 运行示例
└── README.md                                # 说明文档
```

## 安装依赖

```bash
pip install pandas openpyxl requests
```

## 使用方法

### 方法1: 使用命令行

```bash
python consistency_checker.py --excel "新标签28_cut_4_16164674_1752728313.xlsx" --api-url "http://your-api-server:8080" --batch-size 5 --output "result.json"
```

参数说明：
- `--excel`: Excel 文件路径（必需）
- `--api-url`: API 服务器地址（默认: http://localhost:8080）
- `--batch-size`: 批处理大小（默认: 5）
- `--output`: 结果输出文件路径（可选）

### 方法2: 使用 Python 脚本

```python
from consistency_checker import ConsistencyChecker

# 创建检查器
checker = ConsistencyChecker(
    api_base_url="http://your-api-server:8080",
    batch_size=5
)

# 运行检查
result = checker.run_consistency_check(
    excel_path="新标签28_cut_4_16164674_1752728313.xlsx",
    output_path="result.json"
)

print(f"一致率: {result['summary']['一致率']}")
```

### 方法3: 使用预配置脚本

1. 修改 `config.py` 中的 API 地址
2. 运行: `python run_check.py`

## 配置说明

在 `config.py` 中可以配置：

```python
API_CONFIG = {
    "base_url": "http://your-api-server:8080",  # 修改为实际 API 地址
    "batch_size": 5
}
```

## 输出结果

工具会输出以下信息：

1. **控制台摘要**：
   ```
   一致性检查结果摘要
   ==================
   总数据量: 1000
   一致数量: 850
   不一致数量: 150
   一致率: 85.00%
   ```

2. **详细 JSON 报告**（如果指定了输出文件）：
   ```json
   {
     "api_response": {...},
     "comparison_result": {
       "total_count": 1000,
       "consistent_count": 850,
       "inconsistent_count": 150,
       "consistency_rate": 0.85,
       "inconsistent_details": [...]
     },
     "summary": {...}
   }
   ```

## 数据格式要求

Excel 文件应包含以下列：
- `context`: 输入内容/提示词
- `message`: 期望的输出结果
- `model`: 模型名称
- `max_tokens`: 最大 token 数
- `temperature`: 温度参数
- `top_p`: top_p 参数
- `top_k`: top_k 参数
- `role`: 角色
- `user`: 用户标识

## API 接口说明

工具调用的是 `batchCallLargeModel` 接口：

- **接口路径**: `/requestLargeModel/batchAskToFriday`
- **请求方法**: `POST`
- **Content-Type**: `application/json`

请求格式：
```json
{
  "bachsize": 5,
  "fridayHttpRequests": [
    {
      "invocationMethod": "chat",
      "model": "beauty_model_v2_0827",
      "context": "输入内容",
      "maxTokens": 500,
      "topP": 0.7,
      "topK": 1,
      "temperature": 0.01,
      "role": "user",
      "user": "user_0",
      "timeout": 15000
    }
  ]
}
```

## 注意事项

1. 确保 API 服务器正在运行并且可以访问
2. 检查 Excel 文件中的数据格式是否正确
3. 根据实际情况调整批处理大小，避免超时
4. 大量数据检查可能需要较长时间，请耐心等待

## 快速开始

1. **安装依赖**：
   ```bash
   pip install pandas openpyxl requests
   ```

2. **测试工具**：
   ```bash
   python test_tool.py
   ```

3. **配置 API 地址**：
   修改 `config.py` 中的 `base_url`

4. **运行检查**：
   ```bash
   python run_check.py
   ```

## 示例文件

- `test_tool.py` - 测试工具基本功能
- `example_usage.py` - 详细使用示例
- `run_check.py` - 简单运行脚本

## 故障排除

1. **连接错误**：检查 API 地址是否正确
2. **超时错误**：减小批处理大小或增加超时时间
3. **数据格式错误**：检查 Excel 文件列名和数据格式
4. **权限错误**：确保有访问 API 的权限

## 扩展功能

可以根据需要扩展以下功能：
- 支持更复杂的消息比较逻辑
- 添加更多统计指标
- 支持多种输出格式
- 添加进度条显示
- 支持断点续传
