#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一致性检查工具
根据 markdown 接口文档调用 API，并与 xlsx 文件中的 message 字段进行一致性对比
"""

import pandas as pd
import requests
import json
import time
from typing import List, Dict, Any
import argparse
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConsistencyChecker:
    def __init__(self, api_base_url: str = None, batch_size: int = 5):
        """
        初始化一致性检查器
        
        Args:
            api_base_url: API 基础 URL
            batch_size: 批处理大小
        """
        self.api_base_url = api_base_url or "http://localhost:8080"  # 默认本地地址
        self.batch_size = batch_size
        self.api_endpoint = "/requestLargeModel/batchAskToFriday"
        
    def load_excel_data(self, excel_path: str) -> pd.DataFrame:
        """
        加载 Excel 数据
        
        Args:
            excel_path: Excel 文件路径
            
        Returns:
            DataFrame: 加载的数据
        """
        logger.info(f"正在加载 Excel 文件: {excel_path}")
        df = pd.read_excel(excel_path)
        logger.info(f"成功加载 {len(df)} 行数据")
        return df
    
    def prepare_api_requests(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        根据 DataFrame 数据准备 API 请求
        
        Args:
            df: 包含请求数据的 DataFrame
            
        Returns:
            List[Dict]: API 请求列表
        """
        requests_list = []
        
        for index, row in df.iterrows():
            # 构造 FridayHttpRequest 对象
            friday_request = {
                "invocationMethod": "chat",  # 默认使用 chat 方式
                "model": row.get('model', 'beauty_model_v2_0827'),  # 使用数据中的模型或默认值
                "context": row.get('context', ''),  # 使用数据中的 context
                "maxTokens": int(row.get('max_tokens', 500)),  # 最大 token 数
                "topP": float(row.get('top_p', 0.7)),  # top_p 参数
                "topK": int(row.get('top_k', 1)),  # top_k 参数
                "temperature": float(row.get('temperature', 0.01)),  # 温度参数
                "role": row.get('role', 'user'),  # 角色
                "user": f"user_{index}",  # 使用索引作为用户标识
                "timeout": 15000  # 超时时间
            }
            
            requests_list.append(friday_request)
            
        logger.info(f"准备了 {len(requests_list)} 个 API 请求")
        return requests_list
    
    def call_batch_api(self, friday_requests: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        调用批量 API
        
        Args:
            friday_requests: Friday 请求列表
            
        Returns:
            Dict: API 响应结果
        """
        url = f"{self.api_base_url}{self.api_endpoint}"
        
        # 构造请求体
        payload = {
            "bachsize": self.batch_size,  # 注意：接口文档中是 "bachsize" 而不是 "batchsize"
            "fridayHttpRequests": friday_requests
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        logger.info(f"正在调用 API: {url}")
        logger.info(f"请求数量: {len(friday_requests)}, 批处理大小: {self.batch_size}")
        
        try:
            response = requests.post(url, json=payload, headers=headers, timeout=60)
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"API 调用成功，状态码: {result.get('code', 'unknown')}")
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API 调用失败: {e}")
            return {"code": -1, "message": f"请求失败: {e}", "data": None}
    
    def extract_api_messages(self, api_response: Dict[str, Any]) -> Dict[str, str]:
        """
        从 API 响应中提取消息内容
        
        Args:
            api_response: API 响应
            
        Returns:
            Dict: 用户ID到消息内容的映射
        """
        messages = {}
        
        if api_response.get('code') == 0 and api_response.get('data'):
            data = api_response['data']
            for user_id, choice in data.items():
                if isinstance(choice, dict) and 'message' in choice:
                    message = choice['message']
                    if isinstance(message, dict) and 'content' in message:
                        messages[user_id] = message['content']
                        
        return messages
    
    def compare_messages(self, expected_messages: List[str], api_messages: Dict[str, str]) -> Dict[str, Any]:
        """
        比较期望消息和 API 返回消息
        
        Args:
            expected_messages: 期望的消息列表（来自 Excel）
            api_messages: API 返回的消息字典
            
        Returns:
            Dict: 比较结果统计
        """
        total_count = len(expected_messages)
        consistent_count = 0
        inconsistent_details = []
        
        for i, expected_msg in enumerate(expected_messages):
            user_id = f"user_{i}"
            api_msg = api_messages.get(user_id, "")
            
            # 简单的字符串比较（可以根据需要调整比较逻辑）
            is_consistent = expected_msg.strip() == api_msg.strip()
            
            if is_consistent:
                consistent_count += 1
            else:
                inconsistent_details.append({
                    "index": i,
                    "expected": expected_msg,
                    "actual": api_msg
                })
        
        consistency_rate = consistent_count / total_count if total_count > 0 else 0
        
        return {
            "total_count": total_count,
            "consistent_count": consistent_count,
            "inconsistent_count": total_count - consistent_count,
            "consistency_rate": consistency_rate,
            "inconsistent_details": inconsistent_details
        }
    
    def run_consistency_check(self, excel_path: str, output_path: str = None) -> Dict[str, Any]:
        """
        运行完整的一致性检查流程
        
        Args:
            excel_path: Excel 文件路径
            output_path: 结果输出路径（可选）
            
        Returns:
            Dict: 检查结果
        """
        logger.info("开始一致性检查流程")
        
        # 1. 加载数据
        df = self.load_excel_data(excel_path)
        
        # 2. 准备 API 请求
        friday_requests = self.prepare_api_requests(df)
        
        # 3. 调用 API
        api_response = self.call_batch_api(friday_requests)
        
        # 4. 提取 API 消息
        api_messages = self.extract_api_messages(api_response)
        
        # 5. 比较消息
        expected_messages = df['message'].fillna('').astype(str).tolist()
        comparison_result = self.compare_messages(expected_messages, api_messages)
        
        # 6. 生成完整结果
        result = {
            "api_response": api_response,
            "comparison_result": comparison_result,
            "summary": {
                "总数据量": comparison_result["total_count"],
                "一致数量": comparison_result["consistent_count"],
                "不一致数量": comparison_result["inconsistent_count"],
                "一致率": f"{comparison_result['consistency_rate']:.2%}"
            }
        }
        
        # 7. 输出结果
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            logger.info(f"结果已保存到: {output_path}")
        
        # 8. 打印摘要
        self.print_summary(result["summary"], comparison_result["inconsistent_details"][:5])
        
        return result
    
    def print_summary(self, summary: Dict[str, Any], sample_inconsistencies: List[Dict[str, Any]]):
        """
        打印检查结果摘要
        
        Args:
            summary: 摘要信息
            sample_inconsistencies: 不一致样本
        """
        print("\n" + "="*60)
        print("一致性检查结果摘要")
        print("="*60)
        
        for key, value in summary.items():
            print(f"{key}: {value}")
        
        if sample_inconsistencies:
            print("\n不一致样本（前5个）:")
            print("-"*40)
            for item in sample_inconsistencies:
                print(f"索引 {item['index']}:")
                print(f"  期望: {item['expected'][:100]}...")
                print(f"  实际: {item['actual'][:100]}...")
                print()

def main():
    parser = argparse.ArgumentParser(description='API 一致性检查工具')
    parser.add_argument('--excel', required=True, help='Excel 文件路径')
    parser.add_argument('--api-url', default='http://localhost:8080', help='API 基础 URL')
    parser.add_argument('--batch-size', type=int, default=5, help='批处理大小')
    parser.add_argument('--output', help='结果输出文件路径')
    
    args = parser.parse_args()
    
    checker = ConsistencyChecker(api_base_url=args.api_url, batch_size=args.batch_size)
    result = checker.run_consistency_check(args.excel, args.output)
    
    return result

if __name__ == "__main__":
    main()
