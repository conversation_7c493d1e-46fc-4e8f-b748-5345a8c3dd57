#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试一致性检查工具的基本功能
"""

import pandas as pd
from consistency_checker import ConsistencyChecker

def test_data_loading():
    """测试数据加载功能"""
    print("测试数据加载功能...")
    
    checker = ConsistencyChecker()
    
    try:
        df = checker.load_excel_data("新标签28_cut_4_16164674_1752728313.xlsx")
        print(f"✅ 成功加载 {len(df)} 行数据")
        
        # 显示数据基本信息
        print(f"数据列: {list(df.columns)}")
        print(f"非空 message 数量: {df['message'].notna().sum()}")
        print(f"非空 context 数量: {df['context'].notna().sum()}")
        
        return df
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None

def test_request_preparation(df):
    """测试请求准备功能"""
    print("\n测试请求准备功能...")
    
    checker = ConsistencyChecker()
    
    try:
        # 只测试前5行数据
        test_df = df.head(5)
        requests = checker.prepare_api_requests(test_df)
        
        print(f"✅ 成功准备 {len(requests)} 个请求")
        
        # 显示第一个请求的结构
        if requests:
            print("第一个请求示例:")
            first_request = requests[0]
            for key, value in first_request.items():
                if key == 'context':
                    print(f"  {key}: {str(value)[:100]}...")  # 只显示前100个字符
                else:
                    print(f"  {key}: {value}")
        
        return requests
        
    except Exception as e:
        print(f"❌ 请求准备失败: {e}")
        return None

def test_message_comparison():
    """测试消息比较功能"""
    print("\n测试消息比较功能...")
    
    checker = ConsistencyChecker()
    
    # 模拟数据
    expected_messages = [
        '{"匹配结果": "否"}',
        '{"匹配结果": "是"}',
        '{"匹配结果": "否"}'
    ]
    
    api_messages = {
        "user_0": '{"匹配结果": "否"}',  # 一致
        "user_1": '{"匹配结果": "否"}',  # 不一致
        "user_2": '{"匹配结果": "否"}'   # 一致
    }
    
    try:
        result = checker.compare_messages(expected_messages, api_messages)
        
        print(f"✅ 消息比较完成")
        print(f"总数量: {result['total_count']}")
        print(f"一致数量: {result['consistent_count']}")
        print(f"不一致数量: {result['inconsistent_count']}")
        print(f"一致率: {result['consistency_rate']:.2%}")
        
        if result['inconsistent_details']:
            print("不一致详情:")
            for detail in result['inconsistent_details']:
                print(f"  索引 {detail['index']}: 期望 '{detail['expected']}', 实际 '{detail['actual']}'")
        
        return result
        
    except Exception as e:
        print(f"❌ 消息比较失败: {e}")
        return None

def test_mock_api_call():
    """测试模拟 API 调用"""
    print("\n测试模拟 API 调用...")
    
    checker = ConsistencyChecker()
    
    # 模拟请求
    mock_requests = [
        {
            "invocationMethod": "chat",
            "model": "beauty_model_v2_0827",
            "context": "测试内容",
            "maxTokens": 500,
            "topP": 0.7,
            "topK": 1,
            "temperature": 0.01,
            "role": "user",
            "user": "user_0",
            "timeout": 15000
        }
    ]
    
    # 模拟 API 响应
    mock_response = {
        "code": 0,
        "message": "请求成功",
        "data": {
            "user_0": {
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": '{"匹配结果": "否"}'
                },
                "finishReason": "stop"
            }
        }
    }
    
    try:
        # 测试消息提取
        messages = checker.extract_api_messages(mock_response)
        print(f"✅ 成功提取 {len(messages)} 条消息")
        
        for user_id, content in messages.items():
            print(f"  {user_id}: {content}")
        
        return messages
        
    except Exception as e:
        print(f"❌ API 响应处理失败: {e}")
        return None

def main():
    """运行所有测试"""
    print("开始测试一致性检查工具...")
    print("=" * 50)
    
    # 测试数据加载
    df = test_data_loading()
    if df is None:
        return
    
    # 测试请求准备
    requests = test_request_preparation(df)
    if requests is None:
        return
    
    # 测试消息比较
    comparison_result = test_message_comparison()
    if comparison_result is None:
        return
    
    # 测试模拟 API 调用
    api_messages = test_mock_api_call()
    if api_messages is None:
        return
    
    print("\n" + "=" * 50)
    print("✅ 所有测试通过！工具基本功能正常。")
    print("\n📝 使用说明:")
    print("1. 确保 API 服务器正在运行")
    print("2. 修改 config.py 中的 API 地址")
    print("3. 运行: python run_check.py")
    print("4. 或使用命令行: python consistency_checker.py --excel '新标签28_cut_4_16164674_1752728313.xlsx' --api-url 'http://your-api:8080'")

if __name__ == "__main__":
    main()
