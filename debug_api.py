#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试 API 请求的脚本
"""

import pandas as pd
import json
import requests
from consistency_checker import Consistency<PERSON>he<PERSON>

def debug_single_request():
    """调试单个请求"""
    print("=== 调试单个 API 请求 ===")
    
    # 读取数据
    df = pd.read_excel("新标签28_cut_4_16164674_1752728313.xlsx")
    
    # 只取第一行数据
    first_row = df.iloc[0]
    
    print("原始数据:")
    print(f"context: {first_row['context'][:200]}...")
    print(f"message: {first_row['message']}")
    print(f"model: {first_row['model']}")
    
    # 创建检查器并准备请求
    checker = ConsistencyChecker()
    test_df = df.head(1)
    requests_list = checker.prepare_api_requests(test_df)
    
    print("\n准备的请求:")
    first_request = requests_list[0]
    for key, value in first_request.items():
        if key == 'context':
            print(f"{key}: {str(value)[:200]}...")
        else:
            print(f"{key}: {value}")
    
    # 构造完整的 API 请求
    payload = {
        "bachsize": 1,
        "fridayHttpRequests": requests_list
    }
    
    print(f"\n完整请求体大小: {len(json.dumps(payload))} 字符")
    
    # 尝试调用 API
    url = "http://10.109.36.153:8080/requestLargeModel/batchAskToFriday"
    headers = {"Content-Type": "application/json"}
    
    try:
        print(f"\n正在调用 API: {url}")
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code != 200:
            print(f"错误响应内容: {response.text}")
        else:
            result = response.json()
            print(f"成功响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
    except Exception as e:
        print(f"请求异常: {e}")

def debug_request_format():
    """调试请求格式"""
    print("\n=== 调试请求格式 ===")

    # 测试不同的请求格式
    test_cases = [
        {
            "name": "标准格式 (gpt-3.5-turbo)",
            "request": {
                "invocationMethod": "chat",
                "model": "gpt-3.5-turbo",
                "context": "请介绍一下人工智能",
                "maxTokens": 1000,
                "topP": 0.9,
                "topK": 50,
                "temperature": 0.7,
                "role": "user",
                "user": "user001",
                "timeout": 15000
            }
        },
        {
            "name": "美容模型格式",
            "request": {
                "invocationMethod": "chat",
                "model": "beauty_model_v2_0827",
                "context": "测试内容",
                "maxTokens": 500,
                "topP": 0.7,
                "topK": 1,
                "temperature": 0.01,
                "role": "user",
                "user": "test_user",
                "timeout": 15000
            }
        },
        {
            "name": "简化格式",
            "request": {
                "invocationMethod": "chat",
                "model": "beauty_model_v2_0827",
                "context": "测试",
                "user": "test"
            }
        }
    ]

    url = "http://10.109.36.153:8080/requestLargeModel/batchAskToFriday"
    headers = {"Content-Type": "application/json"}

    for test_case in test_cases:
        print(f"\n--- 测试 {test_case['name']} ---")

        payload = {
            "bachsize": 1,
            "fridayHttpRequests": [test_case['request']]
        }

        print(f"请求体: {json.dumps(payload, ensure_ascii=False, indent=2)}")

        try:
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            print(f"响应状态码: {response.status_code}")

            if response.status_code != 200:
                print(f"错误响应: {response.text}")
            else:
                result = response.json()
                print(f"成功响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

        except Exception as e:
            print(f"请求异常: {e}")

def test_api_connectivity():
    """测试 API 连通性"""
    print("\n=== 测试 API 连通性 ===")
    
    url = "http://10.109.36.153:8080"
    
    try:
        # 测试基础连接
        response = requests.get(url, timeout=10)
        print(f"基础连接测试 - 状态码: {response.status_code}")
        
        # 测试接口路径
        api_url = f"{url}/requestLargeModel/batchAskToFriday"
        response = requests.options(api_url, timeout=10)
        print(f"OPTIONS 请求 - 状态码: {response.status_code}")
        print(f"允许的方法: {response.headers.get('Allow', 'N/A')}")
        
    except Exception as e:
        print(f"连接测试失败: {e}")

def compare_with_original_data():
    """与原始数据格式对比"""
    print("\n=== 与原始数据格式对比 ===")
    
    df = pd.read_excel("新标签28_cut_4_16164674_1752728313.xlsx")
    first_row = df.iloc[0]
    
    print("Excel 中的字段:")
    for col in df.columns:
        if col in ['context', 'model', 'max_tokens', 'temperature', 'top_p', 'top_k', 'role', 'user']:
            value = first_row[col]
            if col == 'context':
                print(f"{col}: {str(value)[:100]}... (长度: {len(str(value))})")
            else:
                print(f"{col}: {value}")
    
    # 检查 context 是否是有效的 JSON
    context_raw = first_row['context']
    try:
        context_parsed = json.loads(context_raw)
        print(f"\ncontext JSON 解析成功，类型: {type(context_parsed)}")
        if isinstance(context_parsed, str):
            print(f"解析后内容长度: {len(context_parsed)}")
        else:
            print(f"解析后内容: {context_parsed}")
    except Exception as e:
        print(f"\ncontext JSON 解析失败: {e}")

def main():
    """主函数"""
    print("开始 API 调试...")
    
    # 1. 测试连通性
    test_api_connectivity()
    
    # 2. 对比数据格式
    compare_with_original_data()
    
    # 3. 测试标准格式
    debug_request_format()
    
    # 4. 调试实际请求
    debug_single_request()

if __name__ == "__main__":
    main()
