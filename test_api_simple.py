#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的 API 测试脚本
"""

import requests
import json

def test_api_endpoints():
    """测试不同的 API 端点"""
    base_url = "http://10.109.36.153:8080"
    
    endpoints = [
        "/",
        "/requestLargeModel",
        "/requestLargeModel/batchAskToFriday",
        "/requestLargeModel/askFriday",  # 单次调用接口
    ]
    
    for endpoint in endpoints:
        url = f"{base_url}{endpoint}"
        print(f"\n测试端点: {url}")
        
        # 测试 GET
        try:
            response = requests.get(url, timeout=10)
            print(f"GET - 状态码: {response.status_code}")
            if response.status_code != 404:
                print(f"GET - 响应: {response.text[:200]}...")
        except Exception as e:
            print(f"GET - 异常: {e}")
        
        # 测试 POST (空请求体)
        try:
            response = requests.post(url, timeout=10)
            print(f"POST (空) - 状态码: {response.status_code}")
            if response.status_code != 404:
                print(f"POST (空) - 响应: {response.text[:200]}...")
        except Exception as e:
            print(f"POST (空) - 异常: {e}")

def test_with_query_params():
    """测试使用查询参数的方式"""
    base_url = "http://10.109.36.153:8080"
    endpoint = "/requestLargeModel/batchAskToFriday"
    
    # 尝试使用查询参数
    params = {"bachsize": 1}
    
    # 简单的请求体
    simple_request = {
        "invocationMethod": "chat",
        "model": "beauty_model_v2_0827",
        "context": "测试",
        "user": "test"
    }
    
    payload = {"fridayHttpRequests": [simple_request]}
    
    url = f"{base_url}{endpoint}"
    
    print(f"\n测试查询参数方式: {url}")
    print(f"查询参数: {params}")
    print(f"请求体: {json.dumps(payload, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            url, 
            params=params,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
    except Exception as e:
        print(f"异常: {e}")

def test_different_content_types():
    """测试不同的 Content-Type"""
    base_url = "http://10.109.36.153:8080"
    endpoint = "/requestLargeModel/batchAskToFriday"
    url = f"{base_url}{endpoint}"
    
    simple_payload = {
        "bachsize": 1,
        "fridayHttpRequests": [{
            "invocationMethod": "chat",
            "model": "beauty_model_v2_0827",
            "context": "测试",
            "user": "test"
        }]
    }
    
    content_types = [
        "application/json",
        "application/json;charset=utf-8",
        "application/x-www-form-urlencoded",
        "text/plain"
    ]
    
    for content_type in content_types:
        print(f"\n测试 Content-Type: {content_type}")
        
        headers = {"Content-Type": content_type}
        
        try:
            if content_type.startswith("application/json"):
                response = requests.post(url, json=simple_payload, headers=headers, timeout=30)
            else:
                # 对于非 JSON 类型，发送字符串
                data = json.dumps(simple_payload)
                response = requests.post(url, data=data, headers=headers, timeout=30)
            
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text[:200]}...")
            
        except Exception as e:
            print(f"异常: {e}")

def test_minimal_request():
    """测试最小化请求"""
    base_url = "http://10.109.36.153:8080"
    endpoint = "/requestLargeModel/batchAskToFriday"
    url = f"{base_url}{endpoint}"
    
    # 最小化请求
    minimal_payload = {
        "bachsize": 1,
        "fridayHttpRequests": [{
            "model": "beauty_model_v2_0827",
            "context": "测试",
            "user": "test"
        }]
    }
    
    print(f"\n测试最小化请求:")
    print(f"URL: {url}")
    print(f"请求体: {json.dumps(minimal_payload, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(
            url,
            json=minimal_payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应体: {response.text}")
        
    except Exception as e:
        print(f"异常: {e}")

def main():
    """主函数"""
    print("开始简单 API 测试...")
    
    # 1. 测试端点
    test_api_endpoints()
    
    # 2. 测试查询参数
    test_with_query_params()
    
    # 3. 测试不同 Content-Type
    test_different_content_types()
    
    # 4. 测试最小化请求
    test_minimal_request()

if __name__ == "__main__":
    main()
