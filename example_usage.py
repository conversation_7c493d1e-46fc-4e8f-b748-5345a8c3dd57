#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一致性检查工具使用示例
"""

import json
import sys
from consistency_checker import ConsistencyChecker

def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建检查器实例
    checker = ConsistencyChecker(
        api_base_url="http://localhost:8080",  # 请修改为实际的 API 地址
        batch_size=5
    )
    
    try:
        # 运行一致性检查
        result = checker.run_consistency_check(
            excel_path="新标签28_cut_4_16164674_1752728313.xlsx",
            output_path="consistency_result.json"
        )
        
        print("检查完成！")
        return result
        
    except Exception as e:
        print(f"检查失败: {e}")
        return None

def example_custom_config():
    """自定义配置示例"""
    print("=== 自定义配置示例 ===")
    
    # 自定义配置
    checker = ConsistencyChecker(
        api_base_url="http://your-api-server:8080",  # 自定义 API 地址
        batch_size=10  # 更大的批处理大小
    )
    
    try:
        result = checker.run_consistency_check(
            excel_path="新标签28_cut_4_16164674_1752728313.xlsx",
            output_path="custom_result.json"
        )
        
        # 自定义结果处理
        if result:
            summary = result['summary']
            print(f"\n自定义报告:")
            print(f"处理了 {summary['总数据量']} 条数据")
            print(f"一致率达到 {summary['一致率']}")
            
            if float(summary['一致率'].rstrip('%')) < 80:
                print("⚠️  一致率低于80%，需要关注！")
            else:
                print("✅ 一致率良好")
        
        return result
        
    except Exception as e:
        print(f"检查失败: {e}")
        return None

def example_batch_processing():
    """批量处理示例"""
    print("=== 批量处理示例 ===")
    
    import pandas as pd
    
    # 读取数据
    df = pd.read_excel("新标签28_cut_4_16164674_1752728313.xlsx")
    
    # 分批处理（例如每100条一批）
    batch_size = 100
    total_batches = (len(df) + batch_size - 1) // batch_size
    
    print(f"总共 {len(df)} 条数据，将分 {total_batches} 批处理")
    
    all_results = []
    checker = ConsistencyChecker(api_base_url="http://localhost:8080", batch_size=5)
    
    for i in range(total_batches):
        start_idx = i * batch_size
        end_idx = min((i + 1) * batch_size, len(df))
        
        print(f"处理第 {i+1}/{total_batches} 批 (行 {start_idx+1}-{end_idx})")
        
        # 创建临时文件
        batch_df = df.iloc[start_idx:end_idx]
        temp_file = f"temp_batch_{i}.xlsx"
        batch_df.to_excel(temp_file, index=False)
        
        try:
            # 处理这一批
            result = checker.run_consistency_check(
                excel_path=temp_file,
                output_path=f"batch_{i}_result.json"
            )
            
            if result:
                all_results.append(result['comparison_result'])
            
            # 清理临时文件
            import os
            os.remove(temp_file)
            
        except Exception as e:
            print(f"批次 {i+1} 处理失败: {e}")
    
    # 汇总所有批次结果
    if all_results:
        total_count = sum(r['total_count'] for r in all_results)
        consistent_count = sum(r['consistent_count'] for r in all_results)
        consistency_rate = consistent_count / total_count if total_count > 0 else 0
        
        print(f"\n=== 批量处理汇总 ===")
        print(f"总数据量: {total_count}")
        print(f"一致数量: {consistent_count}")
        print(f"总一致率: {consistency_rate:.2%}")

def example_error_handling():
    """错误处理示例"""
    print("=== 错误处理示例 ===")
    
    checker = ConsistencyChecker(
        api_base_url="http://invalid-url:8080",  # 故意使用无效地址
        batch_size=5
    )
    
    try:
        result = checker.run_consistency_check(
            excel_path="新标签28_cut_4_16164674_1752728313.xlsx"
        )
        
        # 检查 API 调用是否成功
        if result and result['api_response']['code'] != 0:
            print(f"API 调用失败: {result['api_response']['message']}")
            
            # 可以实现降级策略，比如使用缓存结果或默认值
            print("实施降级策略...")
            
        return result
        
    except Exception as e:
        print(f"捕获到异常: {e}")
        print("可以在这里实现错误恢复逻辑")
        return None

def example_result_analysis():
    """结果分析示例"""
    print("=== 结果分析示例 ===")
    
    # 假设我们有一个结果文件
    try:
        with open("consistency_result.json", 'r', encoding='utf-8') as f:
            result = json.load(f)
        
        comparison = result['comparison_result']
        
        print("详细分析:")
        print(f"总样本数: {comparison['total_count']}")
        print(f"一致样本数: {comparison['consistent_count']}")
        print(f"不一致样本数: {comparison['inconsistent_count']}")
        print(f"一致率: {comparison['consistency_rate']:.4f}")
        
        # 分析不一致的模式
        inconsistent_details = comparison['inconsistent_details']
        if inconsistent_details:
            print(f"\n不一致样本分析 (前10个):")
            for i, detail in enumerate(inconsistent_details[:10]):
                print(f"{i+1}. 索引 {detail['index']}")
                print(f"   期望: {detail['expected'][:50]}...")
                print(f"   实际: {detail['actual'][:50]}...")
                print()
        
        # 生成改进建议
        if comparison['consistency_rate'] < 0.8:
            print("🔍 改进建议:")
            print("- 检查模型参数设置")
            print("- 验证输入数据质量")
            print("- 考虑调整 prompt 模板")
        
    except FileNotFoundError:
        print("结果文件不存在，请先运行一致性检查")
    except Exception as e:
        print(f"分析失败: {e}")

def main():
    """主函数 - 选择运行哪个示例"""
    
    if len(sys.argv) > 1:
        example_type = sys.argv[1]
    else:
        print("请选择要运行的示例:")
        print("1. basic - 基本使用")
        print("2. custom - 自定义配置")
        print("3. batch - 批量处理")
        print("4. error - 错误处理")
        print("5. analysis - 结果分析")
        
        choice = input("请输入选择 (1-5): ").strip()
        example_map = {
            '1': 'basic',
            '2': 'custom', 
            '3': 'batch',
            '4': 'error',
            '5': 'analysis'
        }
        example_type = example_map.get(choice, 'basic')
    
    print(f"运行示例: {example_type}")
    print("=" * 50)
    
    if example_type == 'basic':
        example_basic_usage()
    elif example_type == 'custom':
        example_custom_config()
    elif example_type == 'batch':
        example_batch_processing()
    elif example_type == 'error':
        example_error_handling()
    elif example_type == 'analysis':
        example_result_analysis()
    else:
        print(f"未知示例类型: {example_type}")
        print("可用类型: basic, custom, batch, error, analysis")

if __name__ == "__main__":
    main()
