#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行完整数据的一致性检查（分批处理）
"""

import pandas as pd
import json
import time
from consistency_checker import ConsistencyChecker
from config import API_CONFIG, DATA_CONFIG

def run_batch_consistency_check(batch_size=50, api_batch_size=2):
    """
    分批运行一致性检查
    
    Args:
        batch_size: 每批处理的数据量
        api_batch_size: API 批次大小
    """
    print("开始分批一致性检查...")
    
    # 读取完整数据
    df = pd.read_excel(DATA_CONFIG["excel_file"])
    total_rows = len(df)
    
    print(f"总数据量: {total_rows}")
    print(f"数据批次大小: {batch_size}")
    print(f"API 批次大小: {api_batch_size}")
    
    # 计算批次数
    num_batches = (total_rows + batch_size - 1) // batch_size
    print(f"将分 {num_batches} 批处理")
    
    # 创建检查器
    checker = ConsistencyChecker(
        api_base_url=API_CONFIG["base_url"],
        batch_size=api_batch_size
    )
    
    # 存储所有结果
    all_results = []
    total_consistent = 0
    total_processed = 0
    
    for batch_idx in range(num_batches):
        start_idx = batch_idx * batch_size
        end_idx = min((batch_idx + 1) * batch_size, total_rows)
        
        print(f"\n--- 处理批次 {batch_idx + 1}/{num_batches} (行 {start_idx + 1}-{end_idx}) ---")
        
        # 获取当前批次数据
        batch_df = df.iloc[start_idx:end_idx]
        
        # 创建临时文件
        temp_file = f"temp_batch_{batch_idx}.xlsx"
        batch_df.to_excel(temp_file, index=False)
        
        try:
            # 处理当前批次
            start_time = time.time()
            result = checker.run_consistency_check(
                excel_path=temp_file,
                output_path=f"batch_{batch_idx}_result.json"
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            if result:
                comparison = result['comparison_result']
                batch_consistent = comparison['consistent_count']
                batch_total = comparison['total_count']
                batch_rate = comparison['consistency_rate']
                
                total_consistent += batch_consistent
                total_processed += batch_total
                
                print(f"批次结果: {batch_consistent}/{batch_total} ({batch_rate:.2%})")
                print(f"处理时间: {processing_time:.1f}秒")
                
                all_results.append({
                    "batch_index": batch_idx,
                    "start_row": start_idx,
                    "end_row": end_idx - 1,
                    "consistent_count": batch_consistent,
                    "total_count": batch_total,
                    "consistency_rate": batch_rate,
                    "processing_time": processing_time
                })
            else:
                print(f"❌ 批次 {batch_idx + 1} 处理失败")
            
            # 清理临时文件
            import os
            if os.path.exists(temp_file):
                os.remove(temp_file)
                
        except Exception as e:
            print(f"❌ 批次 {batch_idx + 1} 处理异常: {e}")
            continue
        
        # 显示总体进度
        if total_processed > 0:
            overall_rate = total_consistent / total_processed
            print(f"总体进度: {total_processed}/{total_rows} ({total_processed/total_rows:.1%})")
            print(f"总体一致率: {total_consistent}/{total_processed} ({overall_rate:.2%})")
        
        # 短暂休息避免服务器过载
        if batch_idx < num_batches - 1:
            print("等待 2 秒...")
            time.sleep(2)
    
    # 生成最终报告
    final_rate = total_consistent / total_processed if total_processed > 0 else 0
    
    final_report = {
        "summary": {
            "total_processed": total_processed,
            "total_consistent": total_consistent,
            "total_inconsistent": total_processed - total_consistent,
            "overall_consistency_rate": final_rate,
            "num_batches": len(all_results),
            "processing_details": all_results
        }
    }
    
    # 保存最终报告
    with open("final_consistency_report.json", 'w', encoding='utf-8') as f:
        json.dump(final_report, f, ensure_ascii=False, indent=2)
    
    # 打印最终结果
    print("\n" + "="*60)
    print("最终一致性检查结果")
    print("="*60)
    print(f"总处理数量: {total_processed}")
    print(f"一致数量: {total_consistent}")
    print(f"不一致数量: {total_processed - total_consistent}")
    print(f"总体一致率: {final_rate:.2%}")
    print(f"成功处理批次: {len(all_results)}/{num_batches}")
    print(f"详细报告已保存到: final_consistency_report.json")
    
    return final_report

def run_sample_check(sample_size=100):
    """
    运行样本检查
    
    Args:
        sample_size: 样本大小
    """
    print(f"\n开始样本检查 (样本大小: {sample_size})...")
    
    # 读取数据并随机采样
    df = pd.read_excel(DATA_CONFIG["excel_file"])
    sample_df = df.sample(n=min(sample_size, len(df)), random_state=42)
    
    # 创建临时文件
    temp_file = f"sample_{sample_size}.xlsx"
    sample_df.to_excel(temp_file, index=False)
    
    # 创建检查器
    checker = ConsistencyChecker(
        api_base_url=API_CONFIG["base_url"],
        batch_size=2
    )
    
    try:
        start_time = time.time()
        result = checker.run_consistency_check(
            excel_path=temp_file,
            output_path=f"sample_{sample_size}_result.json"
        )
        end_time = time.time()
        
        if result:
            print(f"样本检查完成!")
            print(f"处理时间: {end_time - start_time:.1f}秒")
            print(f"样本一致率: {result['summary']['一致率']}")
            
            # 基于样本估算全量数据的处理时间
            total_rows = len(df)
            estimated_time = (end_time - start_time) * (total_rows / sample_size)
            print(f"预估全量处理时间: {estimated_time/60:.1f}分钟")
        
        # 清理临时文件
        import os
        if os.path.exists(temp_file):
            os.remove(temp_file)
            
        return result
        
    except Exception as e:
        print(f"样本检查失败: {e}")
        return None

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        mode = sys.argv[1]
    else:
        print("请选择运行模式:")
        print("1. sample - 样本检查 (100条)")
        print("2. batch - 分批处理全量数据")
        print("3. quick - 快速测试 (10条)")
        
        choice = input("请输入选择 (1-3): ").strip()
        mode_map = {'1': 'sample', '2': 'batch', '3': 'quick'}
        mode = mode_map.get(choice, 'sample')
    
    print(f"运行模式: {mode}")
    print("=" * 50)
    
    if mode == 'sample':
        run_sample_check(sample_size=100)
    elif mode == 'batch':
        run_batch_consistency_check(batch_size=50, api_batch_size=2)
    elif mode == 'quick':
        run_sample_check(sample_size=10)
    else:
        print(f"未知模式: {mode}")
        print("可用模式: sample, batch, quick")

if __name__ == "__main__":
    main()
