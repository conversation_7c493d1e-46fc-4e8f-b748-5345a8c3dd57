# batchCallLargeModel 接口文档

## 接口概述

`batchCallLargeModel` 是一个批量调用大语言模型的 REST API 接口，支持同时处理多个请求，提高处理效率。该接口基于 Friday SDK 实现，支持 chat 和 text 两种调用方式。

## 基本信息

- **接口路径**: `/requestLargeModel/batchAskToFriday`
- **请求方法**: `POST`
- **Content-Type**: `application/json`
- **控制器**: `AskToFridayController`
- **方法名**: `batchCallLargeModel`

## 请求参数

### 路径参数
无

### 查询参数
- **bachsize** (Integer, 必填): 批处理大小，控制每批次处理的请求数量
  - 类型: `Integer`
  - 必填: 是
  - 限制: 必须大于 0
  - 说明: 用于控制并发处理的批次大小，影响性能和资源使用

### 请求体参数
- **fridayHttpRequests** (List<FridayHttpRequest>, 必填): 批量请求列表

#### FridayHttpRequest 对象结构

| 字段名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| invocationMethod | String | 是 | 调用方式，支持 "chat" 或 "text" | "chat" |
| model | String | 是 | 模型名称 | "gpt-3.5-turbo" |
| context | String | 是 | 输入内容/提示词 | "你好，请介绍一下自己" |
| maxTokens | Integer | 否 | 最大生成token数 | 1000 |
| topP | Double | 否 | 核采样参数 | 0.9 |
| topK | Integer | 否 | Top-K采样参数 | 50 |
| temperature | Double | 否 | 温度参数，控制随机性 | 0.7 |
| role | String | 否 | 角色，默认为 "user" | "user" |
| user | String | 是 | 用户标识，用作返回结果的key | "user123" |
| timeout | Integer | 否 | 超时时间（毫秒） | 15000 |

## 请求示例

```json
{
  "bachsize": 5,
  "fridayHttpRequests": [
    {
      "invocationMethod": "chat",
      "model": "gpt-3.5-turbo",
      "context": "请介绍一下人工智能",
      "maxTokens": 1000,
      "topP": 0.9,
      "topK": 50,
      "temperature": 0.7,
      "role": "user",
      "user": "user001",
      "timeout": 15000
    },
    {
      "invocationMethod": "text",
      "model": "gpt-3.5-turbo",
      "context": "什么是机器学习？",
      "maxTokens": 800,
      "topP": 0.8,
      "temperature": 0.6,
      "role": "user",
      "user": "user002",
      "timeout": 15000
    }
  ]
}
```

## 响应结果

### 响应体结构

```json
{
  "code": 0,
  "message": "请求成功",
  "data": {
    "user001": {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "人工智能（AI）是计算机科学的一个分支..."
      },
      "finishReason": "stop"
    },
    "user002": {
      "index": 0,
      "message": {
        "role": "assistant", 
        "content": "机器学习是人工智能的一个子领域..."
      },
      "finishReason": "stop"
    }
  }
}
```

### 响应字段说明

#### HttpResponse<Map<String, ChatCompletionChoice>> 结构

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | int | 状态码，0表示成功，-1表示失败 |
| message | String | 响应消息 |
| data | Map<String, ChatCompletionChoice> | 响应数据，key为用户标识，value为模型响应 |

#### ChatCompletionChoice 结构

| 字段名 | 类型 | 说明 |
|--------|------|------|
| index | Integer | 选择索引 |
| message | ChatMessage | 消息内容 |
| finishReason | String | 完成原因（stop, length, content_filter等） |

#### ChatMessage 结构

| 字段名 | 类型 | 说明 |
|--------|------|------|
| role | String | 角色（assistant, user, system） |
| content | String | 消息内容 |

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 0 | 请求成功 |
| -1 | 请求失败 |

## 错误响应示例

### 参数错误
```json
{
  "code": -1,
  "message": "请求参数为空",
  "data": null
}
```

### Friday响应为空
```json
{
  "code": -1,
  "message": "Friday响应为空",
  "data": null
}
```

### 批量请求错误
```json
{
  "code": -1,
  "message": "批量请求Friday错误",
  "data": null
}
```

## 业务逻辑说明

1. **参数验证**: 检查请求列表和批次大小是否有效
2. **批次处理**: 根据 `bachsize` 参数将请求分批处理
3. **并发执行**: 使用 CompletableFuture 实现异步并发处理
4. **模型调用**: 根据 `invocationMethod` 选择调用方式：
   - "chat": 调用 `askToFriday` 方法
   - "text": 调用 `askToFridayWithText` 方法
5. **结果聚合**: 将所有批次的结果合并返回

## 性能特性

- **批量处理**: 支持大量请求的批量处理
- **异步并发**: 使用异步编程提高处理效率
- **分批控制**: 通过 bachsize 参数控制并发度，避免资源过载
- **错误隔离**: 单个请求失败不影响其他请求

## 注意事项

1. **用户标识唯一性**: `user` 字段必须在同一批次中保持唯一，作为返回结果的key
2. **批次大小设置**: `bachsize` 应根据系统资源和性能要求合理设置
3. **超时控制**: 建议设置合适的 `timeout` 值，避免长时间等待
4. **模型选择**: 确保指定的模型名称有效且可用
5. **调用方式**: `invocationMethod` 必须为 "chat" 或 "text"

## 相关接口

- **单次调用接口**: `/requestLargeModel/askFriday` - 用于单个请求的处理
- **模型配置接口**: 用于获取和配置模型参数

## 版本信息

- **API版本**: v1.0
- **最后更新**: 2024年
- **维护者**: sunrunlai
