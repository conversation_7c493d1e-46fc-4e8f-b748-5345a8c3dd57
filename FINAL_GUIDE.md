# API 一致性检查工具 - 最终使用指南

## 🎯 项目完成状态

### ✅ 已完成功能
- **数据处理**：Excel 文件读取，JSON context 字段解析
- **API 调用**：完整的请求构造和批量处理
- **结果比较**：精确的字符串匹配和统计分析
- **报告生成**：详细的 JSON 报告和控制台摘要
- **错误处理**：完善的异常处理和日志记录

### ✅ 测试验证
- **功能测试**：所有核心功能测试通过
- **模拟测试**：使用模拟 API 验证完整流程
- **数据分析**：真实数据分布分析（93.2% "否"，6.8% "是"）
- **一致率测试**：不同场景下的一致率计算正确

## 🚀 立即可用的功能

### 1. 模拟 API 测试
```bash
# 使用模拟 API 测试完整功能
python3 mock_api_test.py
```

### 2. 工具功能验证
```bash
# 验证所有基础功能
python3 test_tool.py
```

### 3. 数据处理测试
```bash
# 测试数据读取和处理
python3 -c "
from consistency_checker import ConsistencyChecker
import pandas as pd

checker = ConsistencyChecker()
df = checker.load_excel_data('新标签28_cut_4_16164674_1752728313.xlsx')
requests = checker.prepare_api_requests(df.head(5))
print(f'成功处理 {len(requests)} 个请求')
"
```

## ⚠️ API 调用问题

### 当前状况
- API 端点存在：`http://10.109.36.153:8080/requestLargeModel/batchAskToFriday`
- 所有请求返回 400 Bad Request 错误
- 可能原因：认证问题、参数格式问题、服务器配置问题

### 调试步骤
1. **检查 API 文档**：确认是否需要认证头或特殊参数
2. **联系 API 提供方**：确认正确的调用方式
3. **检查网络配置**：确认是否需要 VPN 或特殊网络设置

### 临时解决方案
使用模拟 API 测试工具功能：
```python
from mock_api_test import MockConsistencyChecker

checker = MockConsistencyChecker()
result = checker.run_consistency_check(
    excel_path="新标签28_cut_4_16164674_1752728313.xlsx",
    output_path="result.json"
)
```

## 📊 数据分析结果

### 真实数据分布
- **总数据量**：1000 条
- **"否" 响应**：932 条 (93.2%)
- **"是" 响应**：68 条 (6.8%)

### 预期一致率
如果 API 正常工作且模型表现稳定，预期一致率应该在 **85-95%** 之间。

## 🛠️ 工具使用方法

### 方法1：命令行使用
```bash
python3 consistency_checker.py \
  --excel "新标签28_cut_4_16164674_1752728313.xlsx" \
  --api-url "http://10.109.36.153:8080" \
  --batch-size 5 \
  --output "result.json"
```

### 方法2：Python 脚本
```python
from consistency_checker import ConsistencyChecker

checker = ConsistencyChecker(
    api_base_url="http://10.109.36.153:8080",
    batch_size=5
)

result = checker.run_consistency_check(
    excel_path="新标签28_cut_4_16164674_1752728313.xlsx",
    output_path="result.json"
)

print(f"一致率: {result['summary']['一致率']}")
```

### 方法3：配置文件方式
```bash
# 1. 修改 config.py 中的 API 地址
# 2. 运行预配置脚本
python3 run_check.py
```

## 📁 文件说明

### 核心文件
- `consistency_checker.py` - 主要工具类
- `config.py` - 配置文件
- `run_check.py` - 简单运行脚本

### 测试文件
- `test_tool.py` - 功能测试脚本
- `mock_api_test.py` - 模拟 API 测试
- `debug_api.py` - API 调试工具
- `test_api_simple.py` - 简单 API 测试

### 文档文件
- `README.md` - 详细使用说明
- `SUMMARY.md` - 项目总结
- `FINAL_GUIDE.md` - 最终使用指南

## 🔧 故障排除

### 1. 依赖问题
```bash
pip install pandas openpyxl requests
```

### 2. 数据格式问题
确保 Excel 文件包含必需的列：
- `context` (JSON 字符串格式)
- `message` (期望的输出)
- `model`, `max_tokens`, `temperature`, `top_p`, `top_k`, `role`, `user`

### 3. API 连接问题
```bash
# 测试 API 连通性
python3 test_api_simple.py
```

### 4. 内存问题（大数据量）
```bash
# 使用较小的批处理大小
python3 consistency_checker.py --batch-size 2
```

## 📈 性能优化建议

### 1. 批处理大小
- 小数据量（<100）：batch_size = 5-10
- 中等数据量（100-500）：batch_size = 3-5
- 大数据量（>500）：batch_size = 1-3

### 2. 分批处理
```python
# 对于超大数据量，建议分批处理
from example_usage import example_batch_processing
example_batch_processing()
```

### 3. 并发控制
根据服务器性能调整 `timeout` 和 `batch_size` 参数。

## 🎯 下一步行动

### 立即可做
1. **运行模拟测试**：验证工具功能
2. **分析数据分布**：了解期望的一致率
3. **准备测试数据**：选择代表性的数据子集

### 需要协调
1. **解决 API 认证**：联系 API 提供方
2. **确认调用格式**：验证请求参数是否正确
3. **网络配置**：确认是否需要特殊网络设置

### 长期改进
1. **添加更多比较算法**：语义相似度比较
2. **性能优化**：并发处理和缓存
3. **可视化报告**：图表和仪表板

---

## 📞 支持信息

- **开发者**：sunrunlai
- **项目路径**：`/Users/<USER>/Documents/augment-projects/consistency-check`
- **最后更新**：2025-07-17

**工具状态**：✅ 核心功能完成，⚠️ API 调用待解决

该工具已经完全实现了一致性检查的核心功能，可以立即用于模拟测试和数据分析。一旦解决 API 调用问题，即可投入生产使用。
