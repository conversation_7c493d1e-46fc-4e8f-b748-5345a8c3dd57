#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试小批量数据的一致性检查
"""

import pandas as pd
from consistency_checker import ConsistencyChecker

def test_small_batch():
    """测试小批量数据"""
    print("=== 测试小批量数据一致性检查 ===")
    
    # 读取数据
    df = pd.read_excel("新标签28_cut_4_16164674_1752728313.xlsx")
    
    # 只测试前10行数据
    test_df = df.head(10)
    
    # 创建临时文件
    temp_file = "temp_test_10.xlsx"
    test_df.to_excel(temp_file, index=False)
    
    # 创建检查器，使用更小的批次大小
    checker = ConsistencyChecker(
        api_base_url="http://10.109.36.153:8080",
        batch_size=2  # 更小的批次大小
    )
    
    try:
        result = checker.run_consistency_check(
            excel_path=temp_file,
            output_path="small_batch_result.json"
        )
        
        print(f"\n小批量测试完成!")
        print(f"一致率: {result['summary']['一致率']}")
        
        # 显示详细结果
        comparison = result['comparison_result']
        print(f"\n详细结果:")
        print(f"总数量: {comparison['total_count']}")
        print(f"一致数量: {comparison['consistent_count']}")
        print(f"不一致数量: {comparison['inconsistent_count']}")
        
        # 显示前几个不一致的详情
        if comparison['inconsistent_details']:
            print(f"\n不一致详情（前3个）:")
            for i, detail in enumerate(comparison['inconsistent_details'][:3]):
                print(f"{i+1}. 索引 {detail['index']}")
                print(f"   期望: {detail['expected']}")
                print(f"   实际: {detail['actual']}")
                print()
        
        # 清理临时文件
        import os
        os.remove(temp_file)
        
        return result
        
    except Exception as e:
        print(f"小批量测试失败: {e}")
        return None

def test_single_request():
    """测试单个请求"""
    print("\n=== 测试单个请求 ===")
    
    # 读取数据
    df = pd.read_excel("新标签28_cut_4_16164674_1752728313.xlsx")
    first_row = df.iloc[0]
    
    # 创建检查器
    checker = ConsistencyChecker(
        api_base_url="http://10.109.36.153:8080",
        batch_size=1
    )
    
    # 准备单个请求
    test_df = df.head(1)
    requests = checker.prepare_api_requests(test_df)
    
    print(f"准备的请求:")
    request = requests[0]
    for key, value in request.items():
        if key == 'context':
            print(f"  {key}: {str(value)[:100]}... (长度: {len(str(value))})")
        else:
            print(f"  {key}: {value}")
    
    # 调用 API
    try:
        result = checker.call_batch_api(requests)
        
        print(f"\nAPI 调用结果:")
        print(f"状态码: {result.get('code')}")
        print(f"消息: {result.get('message')}")
        
        if result.get('code') == 0:
            data = result.get('data', {})
            for user_id, response in data.items():
                print(f"\n用户 {user_id} 的响应:")
                print(f"  message: {response.get('message')}")
                print(f"  finish_reason: {response.get('finish_reason')}")
        
        # 提取消息
        api_messages = checker.extract_api_messages(result)
        print(f"\n提取的消息:")
        for user_id, message in api_messages.items():
            print(f"  {user_id}: {message}")
        
        # 与期望结果比较
        expected_message = first_row['message']
        actual_message = api_messages.get('user_0', '')
        
        print(f"\n比较结果:")
        print(f"期望: {expected_message}")
        print(f"实际: {actual_message}")
        print(f"一致: {'是' if expected_message == actual_message else '否'}")
        
        return result
        
    except Exception as e:
        print(f"单个请求测试失败: {e}")
        return None

def analyze_parameter_issue():
    """分析参数问题"""
    print("\n=== 分析参数问题 ===")
    
    import requests
    import json
    
    url = "http://10.109.36.153:8080/requestLargeModel/batchAskToFriday"
    
    # 测试不同的参数组合
    test_cases = [
        {
            "name": "最小参数",
            "request": {
                "invocationMethod": "text",
                "model": "beauty_model_v2_0827",
                "context": "你好",
                "user": "test"
            }
        },
        {
            "name": "添加必需参数",
            "request": {
                "invocationMethod": "text",
                "model": "beauty_model_v2_0827",
                "context": "你好",
                "user": "test",
                "role": "user"
            }
        },
        {
            "name": "添加数值参数",
            "request": {
                "invocationMethod": "text",
                "model": "beauty_model_v2_0827",
                "context": "你好",
                "user": "test",
                "role": "user",
                "maxTokens": 100,
                "temperature": 0.7,
                "topP": 0.9,
                "topK": 50
            }
        },
        {
            "name": "使用 chat 方式",
            "request": {
                "invocationMethod": "chat",
                "model": "beauty_model_v2_0827",
                "context": "你好",
                "user": "test",
                "role": "user"
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n--- 测试: {test_case['name']} ---")
        
        params = {"bachsize": 1}
        payload = [test_case['request']]
        headers = {"Content-Type": "application/json"}
        
        try:
            response = requests.post(
                url,
                params=params,
                json=payload,
                headers=headers,
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"API 状态码: {result.get('code')}")
                print(f"API 消息: {result.get('message')}")
                
                if result.get('data'):
                    for user_id, resp in result['data'].items():
                        finish_reason = resp.get('finish_reason', 'unknown')
                        message = resp.get('message')
                        
                        if message and message.get('content'):
                            print(f"✅ 成功: {message['content'][:50]}...")
                        else:
                            print(f"❌ 失败: {finish_reason}")
            else:
                print(f"HTTP 错误: {response.text}")
                
        except Exception as e:
            print(f"请求异常: {e}")

def main():
    """主函数"""
    print("开始小批量测试...")
    
    # 1. 测试单个请求
    test_single_request()
    
    # 2. 分析参数问题
    analyze_parameter_issue()
    
    # 3. 测试小批量数据
    test_small_batch()

if __name__ == "__main__":
    main()
