#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的 API 调用方式
"""

import requests
import json
from consistency_checker import Consistency<PERSON><PERSON><PERSON>

def test_new_api_format():
    """测试新的 API 格式"""
    url = "http://10.109.36.153:8080/requestLargeModel/batchAskToFriday"
    
    # 根据更新的接口文档构造请求
    params = {"bachsize": 2}  # 查询参数
    
    # 请求体直接是数组
    payload = [
        {
            "invocationMethod": "text",
            "model": "beauty_model_v2_0827",
            "context": "测试内容1",
            "user": "user001"
        },
        {
            "invocationMethod": "text", 
            "model": "beauty_model_v2_0827",
            "context": "测试内容2",
            "user": "user002"
        }
    ]
    
    headers = {"Content-Type": "application/json"}
    
    print("=== 测试新的 API 格式 ===")
    print(f"URL: {url}")
    print(f"查询参数: {params}")
    print(f"请求体: {json.dumps(payload, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(
            url,
            params=params,
            json=payload,
            headers=headers,
            timeout=30
        )
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"\n✅ API 调用成功!")
            print(f"状态码: {result.get('code')}")
            print(f"消息: {result.get('message')}")
            if result.get('data'):
                print(f"返回数据: {json.dumps(result['data'], ensure_ascii=False, indent=2)}")
        else:
            print(f"\n❌ API 调用失败: {response.status_code}")
            
    except Exception as e:
        print(f"\n❌ 请求异常: {e}")

def test_with_consistency_checker():
    """使用更新后的 ConsistencyChecker 测试"""
    print("\n=== 使用 ConsistencyChecker 测试 ===")
    
    checker = ConsistencyChecker(
        api_base_url="http://10.109.36.153:8080",
        batch_size=2
    )
    
    # 构造测试请求
    test_requests = [
        {
            "invocationMethod": "text",
            "model": "beauty_model_v2_0827", 
            "context": "请回答：什么是人工智能？",
            "user": "test_user_1"
        },
        {
            "invocationMethod": "text",
            "model": "beauty_model_v2_0827",
            "context": "请回答：什么是机器学习？", 
            "user": "test_user_2"
        }
    ]
    
    try:
        result = checker.call_batch_api(test_requests)
        
        print(f"API 调用结果:")
        print(f"状态码: {result.get('code')}")
        print(f"消息: {result.get('message')}")
        
        if result.get('code') == 0:
            print(f"✅ 调用成功!")
            data = result.get('data', {})
            for user_id, response in data.items():
                content = response.get('message', {}).get('content', '')
                print(f"用户 {user_id}: {content[:100]}...")
        else:
            print(f"❌ 调用失败: {result.get('message')}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_with_real_data():
    """使用真实数据测试"""
    print("\n=== 使用真实数据测试 ===")
    
    import pandas as pd
    
    try:
        # 读取真实数据
        df = pd.read_excel("新标签28_cut_4_16164674_1752728313.xlsx")
        
        # 创建检查器
        checker = ConsistencyChecker(
            api_base_url="http://10.109.36.153:8080",
            batch_size=2
        )
        
        # 只测试前2行数据
        test_df = df.head(2)
        requests = checker.prepare_api_requests(test_df)
        
        print(f"准备了 {len(requests)} 个请求")
        print(f"第一个请求的 context 长度: {len(requests[0].get('context', ''))}")
        
        # 调用 API
        result = checker.call_batch_api(requests)
        
        print(f"\nAPI 调用结果:")
        print(f"状态码: {result.get('code')}")
        print(f"消息: {result.get('message')}")
        
        if result.get('code') == 0:
            print(f"✅ 真实数据测试成功!")
            
            # 提取消息并与期望结果比较
            api_messages = checker.extract_api_messages(result)
            expected_messages = test_df['message'].fillna('').astype(str).tolist()
            
            comparison = checker.compare_messages(expected_messages, api_messages)
            
            print(f"\n一致性比较结果:")
            print(f"总数量: {comparison['total_count']}")
            print(f"一致数量: {comparison['consistent_count']}")
            print(f"一致率: {comparison['consistency_rate']:.2%}")
            
        else:
            print(f"❌ 真实数据测试失败: {result.get('message')}")
            
    except Exception as e:
        print(f"❌ 真实数据测试异常: {e}")

def test_different_models():
    """测试不同的模型"""
    print("\n=== 测试不同模型 ===")
    
    url = "http://10.109.36.153:8080/requestLargeModel/batchAskToFriday"
    
    # 测试不同的模型名称
    models_to_test = [
        "beauty_model_v2_0827",
        "gpt-3.5-turbo",
        "gpt-4",
        "claude-3"
    ]
    
    for model in models_to_test:
        print(f"\n--- 测试模型: {model} ---")
        
        params = {"bachsize": 1}
        payload = [{
            "invocationMethod": "text",
            "model": model,
            "context": "你好",
            "user": f"test_{model.replace('-', '_').replace('.', '_')}"
        }]
        
        headers = {"Content-Type": "application/json"}
        
        try:
            response = requests.post(
                url,
                params=params,
                json=payload,
                headers=headers,
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    print(f"✅ 模型 {model} 可用")
                else:
                    print(f"❌ 模型 {model} 返回错误: {result.get('message')}")
            else:
                print(f"❌ HTTP 错误: {response.text[:100]}...")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")

def main():
    """主函数"""
    print("开始测试更新后的 API 调用方式...")
    
    # 1. 测试新的 API 格式
    test_new_api_format()
    
    # 2. 使用 ConsistencyChecker 测试
    test_with_consistency_checker()
    
    # 3. 使用真实数据测试
    test_with_real_data()
    
    # 4. 测试不同模型
    test_different_models()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
