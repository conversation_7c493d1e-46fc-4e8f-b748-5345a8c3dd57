#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同请求头的脚本
"""

import requests
import json

def test_different_headers():
    """测试不同的请求头组合"""
    url = "http://*************:8080/requestLargeModel/batchAskToFriday"
    
    # 简单的测试请求
    payload = {
        "bachsize": 1,
        "fridayHttpRequests": [{
            "invocationMethod": "text",
            "model": "beauty_model_v2_0827",
            "context": "测试",
            "user": "test"
        }]
    }
    
    # 不同的请求头组合
    header_combinations = [
        {
            "name": "基础 JSON",
            "headers": {"Content-Type": "application/json"}
        },
        {
            "name": "带 User-Agent",
            "headers": {
                "Content-Type": "application/json",
                "User-Agent": "ConsistencyChecker/1.0"
            }
        },
        {
            "name": "带 Accept",
            "headers": {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
        },
        {
            "name": "可能的认证头",
            "headers": {
                "Content-Type": "application/json",
                "Authorization": "Bearer test-token",
                "X-API-Key": "test-key"
            }
        },
        {
            "name": "完整请求头",
            "headers": {
                "Content-Type": "application/json; charset=utf-8",
                "Accept": "application/json",
                "User-Agent": "ConsistencyChecker/1.0",
                "Accept-Encoding": "gzip, deflate",
                "Connection": "keep-alive"
            }
        }
    ]
    
    for combo in header_combinations:
        print(f"\n--- 测试: {combo['name']} ---")
        print(f"请求头: {combo['headers']}")
        
        try:
            response = requests.post(
                url, 
                json=payload, 
                headers=combo['headers'], 
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print(f"响应内容: {response.text[:200]}...")
            
        except Exception as e:
            print(f"请求异常: {e}")

def test_minimal_payload():
    """测试最小化的请求体"""
    url = "http://*************:8080/requestLargeModel/batchAskToFriday"
    
    # 逐步减少请求参数
    test_payloads = [
        {
            "name": "完整参数",
            "payload": {
                "bachsize": 1,
                "fridayHttpRequests": [{
                    "invocationMethod": "text",
                    "model": "beauty_model_v2_0827",
                    "context": "测试",
                    "maxTokens": 500,
                    "topP": 0.7,
                    "topK": 1,
                    "temperature": 0.01,
                    "role": "user",
                    "user": "test",
                    "timeout": 15000
                }]
            }
        },
        {
            "name": "必需参数",
            "payload": {
                "bachsize": 1,
                "fridayHttpRequests": [{
                    "invocationMethod": "text",
                    "model": "beauty_model_v2_0827",
                    "context": "测试",
                    "user": "test"
                }]
            }
        },
        {
            "name": "最小参数",
            "payload": {
                "bachsize": 1,
                "fridayHttpRequests": [{
                    "model": "beauty_model_v2_0827",
                    "context": "测试"
                }]
            }
        }
    ]
    
    headers = {"Content-Type": "application/json"}
    
    for test in test_payloads:
        print(f"\n--- 测试: {test['name']} ---")
        print(f"请求体: {json.dumps(test['payload'], ensure_ascii=False, indent=2)}")
        
        try:
            response = requests.post(
                url, 
                json=test['payload'], 
                headers=headers, 
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text}")
            
        except Exception as e:
            print(f"请求异常: {e}")

def test_url_variations():
    """测试不同的 URL 变体"""
    base_url = "http://*************:8080"
    
    url_variations = [
        "/requestLargeModel/batchAskToFriday",
        "/requestLargeModel/batchAskToFriday/",
        "/api/requestLargeModel/batchAskToFriday",
        "/v1/requestLargeModel/batchAskToFriday",
        "/requestLargeModel/batchAskToFriday?version=1"
    ]
    
    payload = {
        "bachsize": 1,
        "fridayHttpRequests": [{
            "invocationMethod": "text",
            "model": "beauty_model_v2_0827",
            "context": "测试",
            "user": "test"
        }]
    }
    
    headers = {"Content-Type": "application/json"}
    
    for endpoint in url_variations:
        url = f"{base_url}{endpoint}"
        print(f"\n--- 测试 URL: {url} ---")
        
        try:
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            print(f"状态码: {response.status_code}")
            if response.status_code != 404:
                print(f"响应: {response.text[:200]}...")
                
        except Exception as e:
            print(f"请求异常: {e}")

def main():
    """主函数"""
    print("开始详细的 API 调试...")
    
    # 1. 测试不同请求头
    test_different_headers()
    
    # 2. 测试最小化请求体
    test_minimal_payload()
    
    # 3. 测试不同 URL
    test_url_variations()

if __name__ == "__main__":
    main()
